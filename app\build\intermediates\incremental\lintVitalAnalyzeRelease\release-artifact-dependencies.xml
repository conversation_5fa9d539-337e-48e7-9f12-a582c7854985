<dependencies>
  <compile
      roots="com.unity3d.ads:unity-ads:4.9.2@aar,com.google.android.material:material:1.12.0@aar,androidx.constraintlayout:constraintlayout:2.0.1@aar,androidx.appcompat:appcompat:1.6.1@aar,com.google.android.gms:play-services-ads:23.5.0@aar,com.google.android.gms:play-services-ads-lite:23.5.0@aar,com.google.android.gms:play-services-ads-base:23.5.0@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.gms:play-services-appset:16.0.1@aar,com.google.android.gms:play-services-cronet:18.0.1@aar,com.google.android.gms:play-services-base:18.0.1@aar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar,com.google.android.ump:user-messaging-platform:3.0.0@aar,com.google.android.gms:play-services-measurement-base:20.1.2@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.fragment:fragment:1.3.6@aar,androidx.fragment:fragment:1.3.6@aar,androidx.activity:activity:1.10.1@aar,androidx.activity:activity-ktx:1.10.1@aar,androidx.activity:activity-compose:1.10.1@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.browser:browser:1.8.0@aar,androidx.webkit:webkit:1.11.0-alpha02@aar,androidx.webkit:webkit:1.11.0-alpha02@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.transition:transition:1.5.0@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.work:work-runtime-ktx:2.7.0@aar,androidx.work:work-runtime:2.7.0@aar,androidx.compose.material3:material3-android:1.3.0@aar,androidx.compose.ui:ui-util-android:1.7.0@aar,androidx.compose.ui:ui-unit-android:1.7.0@aar,androidx.compose.ui:ui-text-android:1.7.0@aar,androidx.compose.foundation:foundation-layout-android:1.7.0@aar,androidx.compose.material:material-ripple-android:1.7.0@aar,androidx.compose.foundation:foundation-android:1.7.0@aar,androidx.compose.animation:animation-core-android:1.7.0@aar,androidx.compose.animation:animation-android:1.7.0@aar,androidx.compose.ui:ui-geometry-android:1.7.0@aar,androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar,androidx.compose.ui:ui-graphics-android:1.7.0@aar,androidx.compose.material:material-icons-core-android:1.7.0@aar,androidx.compose.ui:ui-android:1.7.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar,androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7@aar,androidx.lifecycle:lifecycle-livedata:2.8.7@aar,androidx.lifecycle:lifecycle-livedata:2.8.7@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar,androidx.lifecycle:lifecycle-process:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar,androidx.core:core-ktx:1.13.1@aar,androidx.core:core-splashscreen:1.0.1@aar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,androidx.datastore:datastore:1.0.0@aar,androidx.compose.runtime:runtime-saveable-android:1.7.1@aar,androidx.compose.runtime:runtime-android:1.7.1@aar,androidx.datastore:datastore-core:1.0.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,com.google.protobuf:protobuf-kotlin-lite:3.21.12@jar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-jvm:1.4.0@jar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.8.1@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,androidx.core:core-viewtree:1.0.0@aar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.startup:startup-runtime:1.1.1@aar,com.squareup.okhttp3:okhttp:3.12.13@jar,com.squareup.okio:okio:1.15.0@jar,com.google.protobuf:protobuf-javalite:3.21.12@jar,org.chromium.net:cronet-api:72.3626.96@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.constraintlayout:constraintlayout-solver:2.0.1@jar">
    <dependency
        name="com.unity3d.ads:unity-ads:4.9.2@aar"
        simpleName="com.unity3d.ads:unity-ads"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.android.gms:play-services-ads:23.5.0@aar"
        simpleName="com.google.android.gms:play-services-ads"/>
    <dependency
        name="com.google.android.gms:play-services-ads-lite:23.5.0@aar"
        simpleName="com.google.android.gms:play-services-ads-lite"/>
    <dependency
        name="com.google.android.gms:play-services-ads-base:23.5.0@aar"
        simpleName="com.google.android.gms:play-services-ads-base"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-appset:16.0.1@aar"
        simpleName="com.google.android.gms:play-services-appset"/>
    <dependency
        name="com.google.android.gms:play-services-cronet:18.0.1@aar"
        simpleName="com.google.android.gms:play-services-cronet"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.0.1@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.ump:user-messaging-platform:3.0.0@aar"
        simpleName="com.google.android.ump:user-messaging-platform"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:20.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-ktx:1.10.1@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity-compose:1.10.1@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.webkit:webkit:1.11.0-alpha02@aar"
        simpleName="androidx.webkit:webkit"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.7.0@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.7.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.3.0@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.7.0@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.7.0@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.7.0@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.7.0@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.core:core-splashscreen:1.0.1@aar"
        simpleName="androidx.core:core-splashscreen"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="androidx.datastore:datastore:1.0.0@aar"
        simpleName="androidx.datastore:datastore"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.1@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.1@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="androidx.datastore:datastore-core:1.0.0@jar"
        simpleName="androidx.datastore:datastore-core"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="com.google.protobuf:protobuf-kotlin-lite:3.21.12@jar"
        simpleName="com.google.protobuf:protobuf-kotlin-lite"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:3.12.13@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio:1.15.0@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="com.google.protobuf:protobuf-javalite:3.21.12@jar"
        simpleName="com.google.protobuf:protobuf-javalite"/>
    <dependency
        name="org.chromium.net:cronet-api:72.3626.96@aar"
        simpleName="org.chromium.net:cronet-api"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
        simpleName="androidx.constraintlayout:constraintlayout-solver"/>
  </compile>
  <package
      roots="com.unity3d.ads:unity-ads:4.9.2@aar,com.google.android.material:material:1.12.0@aar,androidx.constraintlayout:constraintlayout:2.0.1@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.appcompat:appcompat:1.6.1@aar,com.google.android.gms:play-services-ads:23.5.0@aar,com.google.android.gms:play-services-ads-lite:23.5.0@aar,com.google.android.gms:play-services-ads-base:23.5.0@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.gms:play-services-appset:16.0.1@aar,com.google.android.gms:play-services-cronet:18.0.1@aar,com.google.android.gms:play-services-base:18.0.1@aar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar,com.google.android.ump:user-messaging-platform:3.0.0@aar,com.google.android.gms:play-services-measurement-base:20.1.2@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.fragment:fragment:1.3.6@aar,androidx.fragment:fragment:1.3.6@aar,androidx.activity:activity:1.10.1@aar,androidx.compose.material3:material3-android:1.3.0@aar,androidx.compose.animation:animation-core-android:1.7.0@aar,androidx.compose.material:material-ripple-android:1.7.0@aar,androidx.compose.animation:animation-android:1.7.0@aar,androidx.compose.foundation:foundation-layout-android:1.7.0@aar,androidx.compose.foundation:foundation-android:1.7.0@aar,androidx.compose.ui:ui-unit-android:1.7.0@aar,androidx.compose.ui:ui-geometry-android:1.7.0@aar,androidx.compose.ui:ui-util-android:1.7.0@aar,androidx.compose.ui:ui-text-android:1.7.0@aar,androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar,androidx.compose.ui:ui-graphics-android:1.7.0@aar,androidx.compose.material:material-icons-core-android:1.7.0@aar,androidx.compose.ui:ui-android:1.7.0@aar,androidx.activity:activity-ktx:1.10.1@aar,androidx.activity:activity-compose:1.10.1@aar,androidx.core:core-splashscreen:1.0.1@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.work:work-runtime-ktx:2.7.0@aar,androidx.work:work-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-service:2.8.7@aar,androidx.transition:transition:1.5.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.8.7@aar,androidx.lifecycle:lifecycle-livedata:2.8.7@aar,androidx.lifecycle:lifecycle-common-java8:2.8.7@jar,androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.browser:browser:1.8.0@aar,androidx.webkit:webkit:1.11.0-alpha02@aar,androidx.webkit:webkit:1.11.0-alpha02@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.autofill:autofill:1.0.0@aar,androidx.graphics:graphics-path:1.0.1@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-process:2.8.7@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar,androidx.datastore:datastore:1.0.0@aar,com.google.protobuf:protobuf-kotlin-lite:3.21.12@jar,androidx.datastore:datastore-core:1.0.0@jar,androidx.compose.runtime:runtime-saveable-android:1.7.1@aar,androidx.compose.runtime:runtime-android:1.7.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,androidx.core:core-viewtree:1.0.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.profileinstaller:profileinstaller:1.4.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.tracing:tracing:1.0.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.room:room-runtime:2.2.5@aar,androidx.sqlite:sqlite-framework:2.1.0@aar,androidx.sqlite:sqlite:2.1.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.collection:collection-ktx:1.4.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-jvm:1.4.0@jar,androidx.room:room-common:2.2.5@jar,androidx.annotation:annotation-jvm:1.8.1@jar,androidx.annotation:annotation-experimental:1.4.1@aar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:guava:31.1-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,com.squareup.okhttp3:okhttp:3.12.13@jar,com.google.errorprone:error_prone_annotations:2.15.0@jar,com.squareup.okio:okio:1.15.0@jar,com.google.protobuf:protobuf-javalite:3.21.12@jar,org.chromium.net:cronet-api:72.3626.96@aar,androidx.constraintlayout:constraintlayout-solver:2.0.1@jar,com.google.guava:failureaccess:1.0.1@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.12.0@jar,com.google.j2objc:j2objc-annotations:1.3@jar">
    <dependency
        name="com.unity3d.ads:unity-ads:4.9.2@aar"
        simpleName="com.unity3d.ads:unity-ads"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.android.gms:play-services-ads:23.5.0@aar"
        simpleName="com.google.android.gms:play-services-ads"/>
    <dependency
        name="com.google.android.gms:play-services-ads-lite:23.5.0@aar"
        simpleName="com.google.android.gms:play-services-ads-lite"/>
    <dependency
        name="com.google.android.gms:play-services-ads-base:23.5.0@aar"
        simpleName="com.google.android.gms:play-services-ads-base"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-appset:16.0.1@aar"
        simpleName="com.google.android.gms:play-services-appset"/>
    <dependency
        name="com.google.android.gms:play-services-cronet:18.0.1@aar"
        simpleName="com.google.android.gms:play-services-cronet"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.0.1@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.ump:user-messaging-platform:3.0.0@aar"
        simpleName="com.google.android.ump:user-messaging-platform"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:20.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.3.0@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.7.0@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.7.0@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.7.0@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.7.0@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.activity:activity-ktx:1.10.1@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity-compose:1.10.1@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.core:core-splashscreen:1.0.1@aar"
        simpleName="androidx.core:core-splashscreen"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.7.0@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.7.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.webkit:webkit:1.11.0-alpha02@aar"
        simpleName="androidx.webkit:webkit"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.autofill:autofill:1.0.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.graphics:graphics-path:1.0.1@aar"
        simpleName="androidx.graphics:graphics-path"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.datastore:datastore:1.0.0@aar"
        simpleName="androidx.datastore:datastore"/>
    <dependency
        name="com.google.protobuf:protobuf-kotlin-lite:3.21.12@jar"
        simpleName="com.google.protobuf:protobuf-kotlin-lite"/>
    <dependency
        name="androidx.datastore:datastore-core:1.0.0@jar"
        simpleName="androidx.datastore:datastore-core"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.1@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.1@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.room:room-runtime:2.2.5@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.1.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.1.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.room:room-common:2.2.5@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:guava:31.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:3.12.13@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.squareup.okio:okio:1.15.0@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="com.google.protobuf:protobuf-javalite:3.21.12@jar"
        simpleName="com.google.protobuf:protobuf-javalite"/>
    <dependency
        name="org.chromium.net:cronet-api:72.3626.96@aar"
        simpleName="org.chromium.net:cronet-api"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
        simpleName="androidx.constraintlayout:constraintlayout-solver"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.12.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:1.3@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
  </package>
</dependencies>
