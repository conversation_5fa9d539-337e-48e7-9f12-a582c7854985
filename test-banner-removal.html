<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إزالة البانر الأسود</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: #2d2d2d;
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #ffcc00;
        }
        
        .test-title {
            color: #ffcc00;
            text-align: center;
            margin-bottom: 30px;
            font-size: 1.5rem;
        }
        
        .test-button {
            background: linear-gradient(45deg, #ffcc00, #ffa500);
            color: #000;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            box-shadow: 0 4px 15px rgba(255, 204, 0, 0.3);
            display: block;
            margin: 20px auto;
        }
        
        .test-button:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(255, 204, 0, 0.4);
        }
        
        .info-box {
            background: rgba(255, 204, 0, 0.1);
            border: 1px solid #ffcc00;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .status-box {
            background: rgba(74, 222, 128, 0.1);
            border: 1px solid #4ade80;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .error-box {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid #ef4444;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .banner-placeholder {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background-color: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-top: 1px solid #333;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">
            🗑️ اختبار إزالة البانر الأسود
        </h1>
        
        <div class="info-box">
            <p><strong>الهدف من هذا الاختبار:</strong></p>
            <ul>
                <li>اختبار إزالة اللوحة السوداء التي تحتوي على "Banner Ad Space"</li>
                <li>التأكد من عدم ظهور العنصر مرة أخرى</li>
                <li>اختبار وظيفة الإزالة الجديدة</li>
            </ul>
        </div>
        
        <button class="test-button" onclick="createTestBanner()">
            إنشاء بانر تجريبي (للاختبار)
        </button>
        
        <button class="test-button" onclick="testBannerRemoval()">
            اختبار إزالة البانر
        </button>
        
        <button class="test-button" onclick="checkBannerStatus()">
            فحص حالة البانر
        </button>
        
        <div id="status-display" class="status-box" style="display: none;">
            <p id="status-text"></p>
        </div>
    </div>

    <!-- Include AdMob config -->
    <script src="app/src/main/assets/admob-config.js"></script>
    
    <script>
        // Test functions
        function createTestBanner() {
            // Create a test banner similar to the original
            const existingBanner = document.getElementById('banner-ad-container');
            if (existingBanner) {
                showStatus('البانر موجود بالفعل!', 'error');
                return;
            }
            
            const bannerContainer = document.createElement('div');
            bannerContainer.id = 'banner-ad-container';
            bannerContainer.className = 'banner-placeholder';
            bannerContainer.innerHTML = `
                <div style="color: #666; font-size: 12px; text-align: center;">
                    <div>Banner Ad Space</div>
                    <div style="font-size: 10px;">Test Banner</div>
                </div>
            `;
            
            document.body.appendChild(bannerContainer);
            showStatus('تم إنشاء البانر التجريبي بنجاح!', 'success');
        }
        
        function testBannerRemoval() {
            if (typeof AdMobIntegration !== 'undefined' && AdMobIntegration.removeBannerPlaceholder) {
                AdMobIntegration.removeBannerPlaceholder();
                showStatus('تم استدعاء دالة إزالة البانر', 'success');
                
                // Check if banner was actually removed
                setTimeout(() => {
                    checkBannerStatus();
                }, 500);
            } else {
                showStatus('دالة إزالة البانر غير متوفرة!', 'error');
            }
        }
        
        function checkBannerStatus() {
            const bannerContainer = document.getElementById('banner-ad-container');
            const bannerWrapper = document.getElementById('banner-ad-wrapper');
            
            if (!bannerContainer && !bannerWrapper) {
                showStatus('✅ ممتاز! لا يوجد أي بانر في الصفحة', 'success');
            } else {
                let message = '⚠️ تم العثور على عناصر بانر:';
                if (bannerContainer) message += '\n- banner-ad-container موجود';
                if (bannerWrapper) message += '\n- banner-ad-wrapper موجود';
                showStatus(message, 'error');
            }
        }
        
        function showStatus(message, type) {
            const statusDisplay = document.getElementById('status-display');
            const statusText = document.getElementById('status-text');
            
            statusText.textContent = message;
            statusDisplay.style.display = 'block';
            
            // Update styling based on type
            if (type === 'success') {
                statusDisplay.className = 'status-box';
            } else if (type === 'error') {
                statusDisplay.className = 'error-box';
            } else {
                statusDisplay.className = 'info-box';
            }
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                statusDisplay.style.display = 'none';
            }, 5000);
        }
        
        // Initialize test
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            
            // Check initial status
            setTimeout(() => {
                checkBannerStatus();
            }, 1000);
        });
        
        // Test AdMob integration if available
        if (typeof AdMobIntegration !== 'undefined') {
            console.log('AdMob Integration available:', AdMobIntegration);
            
            // Test the init function to see if banner is created
            setTimeout(() => {
                console.log('Testing AdMob init...');
                AdMobIntegration.init();
                
                setTimeout(() => {
                    checkBannerStatus();
                }, 1000);
            }, 2000);
        } else {
            console.log('AdMob Integration not available');
        }
    </script>
</body>
</html>
