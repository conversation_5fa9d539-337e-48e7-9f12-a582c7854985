@echo off
echo ========================================
echo Building Modetaris APK (Optimized)
echo ========================================

echo Setting up environment...
echo JAVA_HOME: C:\Program Files\Java\jdk-24

echo.
echo Starting build process...
echo ========================================

REM Set Java Home and run the working command
set "JAVA_HOME=C:\Program Files\Java\jdk-24"

echo 🔨 Building release APK with AdMob integration...
echo.

REM Execute the working build command
gradlew.bat assembleRelease && if exist app\build\outputs\apk\release\app-release.apk (copy app\build\outputs\apk\release\app-release.apk %USERPROFILE%\Desktop\modetaris.apk /Y && echo APK built and copied successfully.) else (echo APK file not found after build.)

REM Check final result
if exist "%USERPROFILE%\Desktop\modetaris.apk" (
    echo.
    echo ✅ Build completed successfully!
    echo 📱 APK Details:
    echo    Location: %USERPROFILE%\Desktop\modetaris.apk
    echo    Package: com.sidimohamed.modetaris
    echo    Version: 2.6 (26) with AdMob integration
    echo    Features: AdMob Banner + Rewarded Interstitial Ads
    echo.
) else (
    echo.
    echo ❌ Build may have failed or APK not copied to Desktop
    echo Check the messages above for details.
    echo.
)

echo ========================================
echo Build process completed.
echo ========================================
pause
