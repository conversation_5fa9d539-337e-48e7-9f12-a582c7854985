#!/usr/bin/env powershell

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Building Modetaris APK (Optimized)" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "Setting up environment..." -ForegroundColor Yellow
Write-Host "JAVA_HOME: C:\Program Files\Java\jdk-24" -ForegroundColor Gray

Write-Host ""
Write-Host "Starting build process..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan

try {
    Write-Host "🔨 Building release APK with optimized settings..." -ForegroundColor Blue

    # Set JAVA_HOME and run the build command that works
    $env:JAVA_HOME = "C:\Program Files\Java\jdk-24"

    # Execute the working command
    $buildCommand = 'gradlew.bat assembleRelease && if exist app\build\outputs\apk\release\app-release.apk (copy app\build\outputs\apk\release\app-release.apk %USERPROFILE%\Desktop\modetaris.apk /Y && echo APK built and copied successfully.) else (echo APK file not found after build.)'

    Write-Host "Executing: $buildCommand" -ForegroundColor Gray

    cmd.exe /c $buildCommand

    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ Build completed successfully!" -ForegroundColor Green

        # Check if APK was copied to desktop
        $desktopPath = "$env:USERPROFILE\Desktop\modetaris.apk"
        if (Test-Path $desktopPath) {
            Write-Host "✅ APK copied to Desktop successfully!" -ForegroundColor Green
            Write-Host ""
            Write-Host "📱 APK Details:" -ForegroundColor Cyan
            Write-Host "   Location: $desktopPath" -ForegroundColor Gray
            Write-Host "   Package: com.sidimohamed.modetaris" -ForegroundColor Gray
            Write-Host "   Version: 2.6 (26)" -ForegroundColor Gray
            Write-Host "   Size: $([math]::Round((Get-Item $desktopPath).Length / 1MB, 2)) MB" -ForegroundColor Gray
        } else {
            Write-Host "⚠️ APK build succeeded but file not found on Desktop" -ForegroundColor Yellow
        }
    } else {
        throw "Build command failed with exit code: $LASTEXITCODE"
    }
} catch {
    Write-Host ""
    Write-Host "❌ Build failed! Error: $_" -ForegroundColor Red
    Write-Host "Check the error messages above for details." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Build process completed." -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Keep window open
Read-Host "Press Enter to continue..."
