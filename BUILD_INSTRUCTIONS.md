# تعليمات بناء التطبيق - Modetaris with AdMob

## 🚀 طرق البناء المتاحة

### الطريقة 1: الأمر المباشر (الموصى به)
```powershell
$env:JAVA_HOME = "C:\Program Files\Java\jdk-24"; cmd.exe /c "gradlew.bat assembleRelease && if exist app\build\outputs\apk\release\app-release.apk (copy app\build\outputs\apk\release\app-release.apk %USERPROFILE%\Desktop\modetaris.apk /Y && echo APK built and copied successfully.) else (echo APK file not found after build.)"
```

### الطريقة 2: استخدام Batch Script
```cmd
.\build-optimized.bat
```

### الطريقة 3: استخدام PowerShell Script
```powershell
.\build-optimized.ps1
```

## 🔧 متطلبات البناء

### 1. Java Development Kit (JDK)
- **المطلوب:** JDK 24 أو أحدث
- **المسار:** `C:\Program Files\Java\jdk-24`
- **للتحقق:** `java -version`

### 2. Android SDK
- **Android SDK:** API 35 (Android 15)
- **Build Tools:** 35.0.0
- **NDK:** للمعمارية المتعددة

### 3. Gradle
- **الإصدار:** 8.11.1
- **يتم تحميله تلقائياً عبر Gradle Wrapper**

## 📱 معلومات التطبيق

### تفاصيل الإصدار
- **Package Name:** `com.sidimohamed.modetaris`
- **Version Name:** `2.6`
- **Version Code:** `26`
- **Target SDK:** 35 (Android 15)
- **Min SDK:** 24 (Android 7.0)

### الميزات الجديدة في v2.6
- ✅ **AdMob Integration:** نظام إعلانات AdMob كامل
- ✅ **Banner Ads:** إعلانات البانر في أسفل الشاشة
- ✅ **Rewarded Interstitial Ads:** إعلانات مكافئة للتحميل
- ✅ **Unity Ads Backup:** نظام احتياطي للإعلانات
- ✅ **Improved UI:** واجهة محسنة مع دعم الإعلانات

### معرفات الوحدات الإعلانية
- **Banner:** `ca-app-pub-4373910379376809/6035179861`
- **Rewarded Interstitial:** `ca-app-pub-4373910379376809/8741234256`

## 🛠️ استكشاف الأخطاء

### خطأ: JAVA_HOME غير صحيح
```
ERROR: JAVA_HOME is set to an invalid directory
```
**الحل:**
1. تحقق من وجود JDK في المسار:
```powershell
Test-Path "C:\Program Files\Java\jdk-24"
```

2. إذا لم يكن موجوداً، ابحث عن المسار الصحيح:
```powershell
Get-ChildItem "C:\Program Files\Java" -Directory
```

3. استخدم المسار الصحيح في الأمر

### خطأ: Gradle Build Failed
```
BUILD FAILED in Xs
```
**الحلول:**
1. **تنظيف المشروع:**
```cmd
gradlew.bat clean
```

2. **فحص مساحة القرص:** تأكد من وجود مساحة كافية (>2GB)

3. **إعادة تحميل Dependencies:**
```cmd
gradlew.bat --refresh-dependencies
```

### خطأ: APK not found after build
```
APK file not found after build
```
**الحل:**
1. تحقق من المسار:
```
app\build\outputs\apk\release\app-release.apk
```

2. فحص رسائل الخطأ في البناء

3. تأكد من اكتمال عملية البناء

## 📋 خطوات البناء التفصيلية

### 1. التحضير
```powershell
# التأكد من المسار الصحيح
cd C:\Users\<USER>\modetaris

# فحص Java
java -version

# فحص Gradle
.\gradlew.bat --version
```

### 2. التنظيف (اختياري)
```cmd
.\gradlew.bat clean
```

### 3. البناء
```powershell
$env:JAVA_HOME = "C:\Program Files\Java\jdk-24"
.\gradlew.bat assembleRelease
```

### 4. النسخ إلى Desktop
```cmd
copy app\build\outputs\apk\release\app-release.apk %USERPROFILE%\Desktop\modetaris.apk /Y
```

## 🎯 التحقق من نجاح البناء

### 1. فحص وجود APK
```powershell
Test-Path "$env:USERPROFILE\Desktop\modetaris.apk"
```

### 2. فحص حجم الملف
```powershell
Get-Item "$env:USERPROFILE\Desktop\modetaris.apk" | Select-Object Length
```

### 3. معلومات APK
- **الحجم المتوقع:** 15-25 MB
- **التوقيع:** Debug أو Release
- **المعمارية:** Universal (جميع المعماريات)

## 🚀 البناء السريع (One-liner)

### للاستخدام اليومي:
```powershell
$env:JAVA_HOME = "C:\Program Files\Java\jdk-24"; cmd.exe /c "gradlew.bat assembleRelease && if exist app\build\outputs\apk\release\app-release.apk (copy app\build\outputs\apk\release\app-release.apk %USERPROFILE%\Desktop\modetaris.apk /Y && echo APK built and copied successfully.) else (echo APK file not found after build.)"
```

### مع التنظيف:
```powershell
$env:JAVA_HOME = "C:\Program Files\Java\jdk-24"; cmd.exe /c "gradlew.bat clean assembleRelease && if exist app\build\outputs\apk\release\app-release.apk (copy app\build\outputs\apk\release\app-release.apk %USERPROFILE%\Desktop\modetaris.apk /Y && echo APK built and copied successfully.) else (echo APK file not found after build.)"
```

## 📊 أوقات البناء المتوقعة

- **البناء الأول:** 3-5 دقائق
- **البناء التدريجي:** 1-2 دقيقة
- **البناء مع التنظيف:** 2-3 دقائق

## 🔍 فحص التطبيق بعد البناء

### 1. تثبيت APK
```cmd
adb install -r %USERPROFILE%\Desktop\modetaris.apk
```

### 2. اختبار الإعلانات
- افتح التطبيق
- اضغط على أي زر تحميل
- تحقق من ظهور الإعلان المكافئ

### 3. فحص Logs
```cmd
adb logcat | findstr "AdMob\|Unity\|MainActivity"
```

---

## ✅ نجح البناء؟

إذا رأيت الرسالة:
```
APK built and copied successfully.
```

فإن التطبيق جاهز في:
```
%USERPROFILE%\Desktop\modetaris.apk
```

**🎉 مبروك! التطبيق مع AdMob جاهز للاستخدام!**
