<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة اختبار الإعلانات - Modetaris</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #FFD700;
            font-size: 1.3em;
        }
        
        .btn {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border: none;
            color: white;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); }
        .btn-success { background: linear-gradient(45deg, #56ab2f, #a8e6cf); }
        .btn-warning { background: linear-gradient(45deg, #f093fb, #f5576c); }
        .btn-info { background: linear-gradient(45deg, #4facfe, #00f2fe); }
        
        .status-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .emoji {
            font-size: 1.2em;
            margin-left: 8px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .alert-info {
            background: rgba(52, 152, 219, 0.2);
            border-color: #3498db;
        }
        
        .alert-warning {
            background: rgba(241, 196, 15, 0.2);
            border-color: #f1c40f;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 لوحة اختبار الإعلانات</h1>
        
        <div class="alert alert-info">
            <strong>📱 معلومات الوحدات الإعلانية:</strong><br>
            Banner: ca-app-pub-4373910379376809/6035179861<br>
            Rewarded Interstitial: ca-app-pub-4373910379376809/8741234256
        </div>
        
        <div class="test-section">
            <h3>🔧 اختبارات AdMob الأساسية</h3>
            <div class="grid">
                <button class="btn btn-primary" onclick="runTest('testAdMob')">
                    <span class="emoji">🧪</span>اختبار AdMob
                </button>
                <button class="btn btn-success" onclick="runTest('testUnityAds')">
                    <span class="emoji">🎮</span>اختبار Unity Ads
                </button>
                <button class="btn btn-info" onclick="runTest('getAdMobStatus')">
                    <span class="emoji">📊</span>حالة AdMob
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎬 اختبارات الإعلانات المكافئة</h3>
            <div class="grid">
                <button class="btn btn-warning" onclick="runTest('testShowRewardedAd')">
                    <span class="emoji">🎁</span>اختبار الإعلان المكافئ
                </button>
                <button class="btn btn-success" onclick="runTest('testDownloadWithAd')">
                    <span class="emoji">⬬</span>اختبار التحميل مع الإعلان
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🚀 اختبار شامل</h3>
            <button class="btn btn-primary" onclick="runComprehensiveTest()" style="width: 100%;">
                <span class="emoji">🔄</span>تشغيل جميع الاختبارات
            </button>
        </div>
        
        <div class="test-section">
            <h3>📋 نتائج الاختبار</h3>
            <div id="testResults" class="status-display">
                انقر على أي زر لبدء الاختبار...
            </div>
        </div>
        
        <div class="alert alert-warning">
            <strong>⚠️ ملاحظة:</strong> تأكد من أن التطبيق يعمل وأن Android Interface متاح للحصول على نتائج دقيقة.
        </div>
    </div>

    <script>
        let testResults = document.getElementById('testResults');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            testResults.innerHTML += `[${timestamp}] ${message}\n`;
            testResults.scrollTop = testResults.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            testResults.innerHTML = '';
        }
        
        function runTest(testFunction) {
            log(`🧪 تشغيل اختبار: ${testFunction}`);
            
            try {
                if (typeof window[testFunction] === 'function') {
                    window[testFunction]();
                    log(`✅ تم استدعاء ${testFunction} بنجاح`);
                } else if (typeof Android !== 'undefined' && typeof Android[testFunction] === 'function') {
                    Android[testFunction]();
                    log(`✅ تم استدعاء Android.${testFunction} بنجاح`);
                } else {
                    log(`❌ الدالة ${testFunction} غير متاحة`);
                    log(`🔍 التحقق من توفر Android Interface...`);
                    
                    if (typeof Android === 'undefined') {
                        log(`⚠️ Android Interface غير متاح (ربما تعمل في المتصفح)`);
                    } else {
                        log(`✅ Android Interface متاح`);
                        log(`📋 الدوال المتاحة: ${Object.keys(Android).join(', ')}`);
                    }
                }
            } catch (error) {
                log(`❌ خطأ في تشغيل ${testFunction}: ${error.message}`);
            }
        }
        
        function runComprehensiveTest() {
            clearLog();
            log(`🚀 بدء الاختبار الشامل...`);
            log(`========================================`);
            
            const tests = [
                'testAdMob',
                'testUnityAds', 
                'getAdMobStatus',
                'testShowRewardedAd',
                'testDownloadWithAd'
            ];
            
            tests.forEach((test, index) => {
                setTimeout(() => {
                    log(`\n📋 اختبار ${index + 1}/${tests.length}: ${test}`);
                    runTest(test);
                    
                    if (index === tests.length - 1) {
                        setTimeout(() => {
                            log(`\n========================================`);
                            log(`✅ انتهى الاختبار الشامل`);
                        }, 1000);
                    }
                }, index * 2000);
            });
        }
        
        // تحقق من توفر الدوال عند تحميل الصفحة
        window.addEventListener('load', () => {
            log(`📱 تم تحميل لوحة اختبار الإعلانات`);
            log(`🔍 فحص توفر Android Interface...`);
            
            if (typeof Android !== 'undefined') {
                log(`✅ Android Interface متاح`);
                log(`📋 الدوال المتاحة: ${Object.keys(Android).join(', ')}`);
            } else {
                log(`⚠️ Android Interface غير متاح`);
                log(`💡 تأكد من تشغيل هذه الصفحة داخل التطبيق`);
            }
            
            // فحص الدوال JavaScript
            const jsFunctions = ['testAdMob', 'testUnityAds', 'testShowRewardedAd', 'getAdMobStatus'];
            const availableFunctions = jsFunctions.filter(func => typeof window[func] === 'function');
            
            if (availableFunctions.length > 0) {
                log(`✅ دوال JavaScript متاحة: ${availableFunctions.join(', ')}`);
            } else {
                log(`⚠️ لم يتم العثور على دوال JavaScript للاختبار`);
            }
        });
    </script>
</body>
</html>
