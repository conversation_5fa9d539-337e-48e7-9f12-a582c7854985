# دليل اختبار الإعلانات المكافئة - Modetaris App

## 🎯 التأكد من ظهور الإعلان المكافئ عند الضغط على زر التحميل

### 📱 تدفق الإعلانات الحالي:

```
المستخدم يضغط على زر التحميل
           ↓
    handleDownload() في JavaScript
           ↓
Android.requestModDownloadWithAd()
           ↓
    showAdOrDownload() في MainActivity
           ↓
    فحص جاهزية AdMob Rewarded Interstitial
           ↓
    ┌─────────────────┬─────────────────┐
    │   AdMob جاهز    │  AdMob غير جاهز  │
    │                 │                 │
    │ عرض إعلان AdMob │ محاولة Unity Ads│
    │       ↓         │       ↓         │
    │ إكمال الإعلان   │ إكمال الإعلان   │
    │       ↓         │       ↓         │
    └─────────────────┴─────────────────┘
                     ↓
              بدء التحميل الفعلي
```

## 🧪 دوال الاختبار المتاحة:

### 1. اختبار AdMob الأساسي:
```javascript
// في console المتصفح أو من التطبيق
testAdMob()
```
**ما يفعله:** يتحقق من تهيئة AdMob وحالة الإعلانات

### 2. اختبار الإعلان المكافئ مباشرة:
```javascript
testShowRewardedAd()
```
**ما يفعله:** يعرض الإعلان المكافئ مباشرة بدون تحميل

### 3. اختبار التدفق الكامل:
```javascript
testDownloadWithAd()
```
**ما يفعله:** يحاكي الضغط على زر التحميل بالكامل

### 4. فحص حالة AdMob:
```javascript
getAdMobStatus()
```
**ما يفعله:** يعرض معلومات مفصلة عن حالة AdMob

## 🔍 خطوات التحقق من عمل الإعلانات:

### الخطوة 1: فحص التهيئة
```javascript
// في console المتصفح
testAdMob()
```
**النتيجة المتوقعة:**
- ✅ "AdMob is ready! Click download to test."
- أو ⚠️ "AdMob is not ready. Trying to reload..."

### الخطوة 2: اختبار الإعلان المباشر
```javascript
testShowRewardedAd()
```
**النتيجة المتوقعة:**
- 🎬 ظهور إعلان AdMob Rewarded Interstitial
- أو 🔄 ظهور Unity Ads كاحتياط
- ✅ رسالة "Test ad completed! Reward granted."

### الخطوة 3: اختبار التدفق الكامل
```javascript
testDownloadWithAd()
```
**النتيجة المتوقعة:**
- 🎬 "Loading rewarded ad..."
- ظهور الإعلان المكافئ
- ✅ "Ad completed! Starting download..."
- محاولة تحميل ملف اختبار

### الخطوة 4: اختبار من واجهة التطبيق
1. اختر أي مود من القائمة
2. اضغط على زر "تحميل"
3. **يجب أن يظهر:**
   - 🎬 "Loading rewarded ad..."
   - إعلان AdMob Rewarded Interstitial
   - بعد إكمال الإعلان: بدء التحميل

## 🔧 استكشاف الأخطاء:

### المشكلة: الإعلان لا يظهر
**الحلول:**
1. تحقق من الـ logs:
```javascript
getAdMobStatus()
```

2. فرض إعادة تحميل الإعلان:
```javascript
// سيتم تلقائياً إذا لم يكن الإعلان جاهز
testAdMob()
```

3. تحقق من معرفات الوحدات الإعلانية:
- Banner: `ca-app-pub-4373910379376809/6035179861`
- Rewarded Interstitial: `ca-app-pub-4373910379376809/8741234256`

### المشكلة: الإعلان يفشل
**النتيجة المتوقعة:**
- تحويل تلقائي إلى Unity Ads
- أو بدء التحميل مباشرة إذا فشل كلا النظامين

### المشكلة: التحميل لا يبدأ بعد الإعلان
**تحقق من:**
- الـ logs في Android Studio
- معلومات التحميل المعلقة (pendingDownloadUrl)

## 📊 مراقبة الأداء:

### في Android Studio Logcat:
```
🔍 Checking AdMob Rewarded Interstitial status...
✅ AdMob Rewarded Interstitial Ad is ready. Showing ad...
🎬 Loading rewarded ad...
🎬 Showing rewarded interstitial ad...
✅ User earned reward: 1 mods
✅ AdMob ad completed successfully, initiating download...
```

### في Browser Console:
```javascript
// فحص مستمر للحالة
setInterval(() => {
    console.log('AdMob Status:', getAdMobStatus());
}, 5000);
```

## ⚠️ ملاحظات مهمة:

### 1. وضع الاختبار:
- حالياً مفعل للاختبار
- يستخدم معرفات إعلانات Google الاختبارية
- **تذكر إيقافه للإنتاج**

### 2. النظام الاحتياطي:
- Unity Ads يعمل كنظام احتياط
- يضمن عدم توقف التحميل حتى لو فشل AdMob

### 3. تجربة المستخدم:
- رسائل واضحة للمستخدم
- مؤشرات تحميل الإعلان
- تحويل سلس بين الأنظمة

## 🎯 التحقق النهائي:

### ✅ قائمة التحقق:
- [ ] `testAdMob()` يعمل بنجاح
- [ ] `testShowRewardedAd()` يعرض الإعلان
- [ ] `testDownloadWithAd()` يكمل التدفق
- [ ] الضغط على زر التحميل يعرض الإعلان
- [ ] الإعلان يكمل بنجاح
- [ ] التحميل يبدأ بعد الإعلان
- [ ] النظام الاحتياطي يعمل عند الحاجة

### 🚀 للاختبار السريع:
```javascript
// اختبار شامل سريع
console.log('🧪 Starting comprehensive ad test...');
testAdMob();
setTimeout(() => testShowRewardedAd(), 3000);
setTimeout(() => testDownloadWithAd(), 6000);
```

---

**✅ إذا نجحت جميع الاختبارات، فإن نظام الإعلانات المكافئة يعمل بشكل صحيح!**
