<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ملكية التطبيق - Mod Etaris</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: #2d2d2d;
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #ffcc00;
        }
        
        .test-title {
            color: #ffcc00;
            text-align: center;
            margin-bottom: 30px;
            font-size: 1.5rem;
        }
        
        .test-button {
            background: linear-gradient(45deg, #ffcc00, #ffa500);
            color: #000;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            box-shadow: 0 4px 15px rgba(255, 204, 0, 0.3);
            display: block;
            margin: 20px auto;
        }
        
        .test-button:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(255, 204, 0, 0.4);
        }
        
        .language-selector {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .language-btn {
            background: #444;
            color: white;
            border: 1px solid #666;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .language-btn:hover {
            background: #555;
        }
        
        .language-btn.active {
            background: #ffcc00;
            color: #000;
        }
        
        .info-box {
            background: rgba(255, 204, 0, 0.1);
            border: 1px solid #ffcc00;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">
            <i class="fas fa-certificate"></i>
            اختبار ميزة ملكية التطبيق
        </h1>
        
        <div class="language-selector">
            <button class="language-btn active" onclick="setLanguage('ar')">العربية</button>
            <button class="language-btn" onclick="setLanguage('en')">English</button>
        </div>
        
        <div class="info-box">
            <p><strong>الهدف من هذا الاختبار:</strong></p>
            <ul>
                <li>اختبار عرض نافذة ملكية التطبيق</li>
                <li>التأكد من صحة النصوص باللغتين العربية والإنجليزية</li>
                <li>التحقق من التصميم والألوان</li>
                <li>اختبار وظائف الإغلاق والتفاعل</li>
            </ul>
        </div>
        
        <button class="test-button" onclick="showAppOwnershipModal()">
            <i class="fas fa-certificate"></i>
            عرض نافذة ملكية التطبيق
        </button>
        
        <div class="info-box">
            <p><strong>معلومات المطور:</strong></p>
            <p>اسم المطور: Sidi Mohamed</p>
            <p>البريد الإلكتروني: <EMAIL></p>
            <p>اسم الحزمة: com.sidimohamed.modetaris</p>
        </div>
    </div>

    <!-- Include translation system -->
    <script src="app/src/main/assets/translations.js"></script>
    
    <script>
        // Language switching function
        function setLanguage(lang) {
            localStorage.setItem('selectedLanguage', lang);
            if (window.translationManager) {
                window.translationManager.setLanguage(lang);
            }
            
            // Update button states
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update direction
            if (lang === 'ar') {
                document.documentElement.dir = 'rtl';
                document.documentElement.lang = 'ar';
            } else {
                document.documentElement.dir = 'ltr';
                document.documentElement.lang = 'en';
            }
            
            console.log('Language changed to:', lang);
        }
        
        // Helper function for translations
        function t(key, ...args) {
            if (window.translationManager) {
                return window.translationManager.t(key, ...args);
            }
            return key;
        }
        
        // App Ownership Modal Function (copied from script.js)
        function showAppOwnershipModal() {
            // Remove existing modal if any
            const existingModal = document.getElementById('app-ownership-modal');
            if (existingModal) {
                existingModal.remove();
            }

            const isArabic = (localStorage.getItem('selectedLanguage') || 'en') === 'ar';

            // Create modal overlay
            const modalOverlay = document.createElement('div');
            modalOverlay.id = 'app-ownership-modal';
            modalOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 100010;
                padding: 20px;
                box-sizing: border-box;
            `;

            // Create modal container
            const modalContainer = document.createElement('div');
            modalContainer.style.cssText = `
                background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
                border-radius: 15px;
                max-width: 700px;
                width: 100%;
                max-height: 85vh;
                overflow-y: auto;
                position: relative;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
                border: 2px solid #ffcc00;
                padding: 30px;
                box-sizing: border-box;
                direction: ${isArabic ? 'rtl' : 'ltr'};
                text-align: ${isArabic ? 'right' : 'left'};
            `;

            // Create close button
            const closeButton = document.createElement('button');
            closeButton.innerHTML = '&times;';
            closeButton.style.cssText = `
                position: absolute;
                top: 15px;
                ${isArabic ? 'left' : 'right'}: 15px;
                background: transparent;
                border: none;
                color: #ffcc00;
                font-size: 1.8rem;
                cursor: pointer;
                width: 35px;
                height: 35px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background-color 0.2s;
            `;
            closeButton.onmouseover = () => closeButton.style.backgroundColor = 'rgba(255,204,0,0.2)';
            closeButton.onmouseout = () => closeButton.style.backgroundColor = 'transparent';
            closeButton.onclick = () => modalOverlay.remove();

            // Create content
            const content = document.createElement('div');
            content.innerHTML = `
                <!-- Title -->
                <h2 style="
                    color: #ffcc00;
                    font-size: 1.6rem;
                    font-weight: bold;
                    margin-bottom: 25px;
                    text-align: center;
                    font-family: 'Press Start 2P', 'Minecraft-Fallback', 'Courier New', monospace;
                    text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.8);
                ">
                    <i class="fas fa-certificate" style="margin-${isArabic ? 'left' : 'right'}: 10px;"></i>
                    ${t('app_ownership_title')}
                </h2>

                <!-- Ownership Verification Section -->
                <div style="margin-bottom: 25px;">
                    <h3 style="
                        color: #4ade80;
                        font-size: 1.2rem;
                        font-weight: bold;
                        margin-bottom: 15px;
                        border-bottom: 2px solid #4ade80;
                        padding-bottom: 8px;
                    ">
                        <i class="fas fa-shield-alt" style="margin-${isArabic ? 'left' : 'right'}: 8px;"></i>
                        ${t('ownership_verification_title')}
                    </h3>
                    <div style="
                        background: rgba(74, 222, 128, 0.1);
                        border: 1px solid #4ade80;
                        border-radius: 8px;
                        padding: 15px;
                        margin-bottom: 15px;
                    ">
                        <p style="
                            color: #ddd;
                            line-height: 1.6;
                            font-size: 1rem;
                            margin: 0;
                            font-weight: bold;
                        ">
                            ${t('ownership_verification_text')}
                        </p>
                    </div>
                </div>

                <!-- Developer Information Section -->
                <div style="margin-bottom: 25px;">
                    <h3 style="
                        color: #60a5fa;
                        font-size: 1.2rem;
                        font-weight: bold;
                        margin-bottom: 15px;
                        border-bottom: 2px solid #60a5fa;
                        padding-bottom: 8px;
                    ">
                        <i class="fas fa-user-tie" style="margin-${isArabic ? 'left' : 'right'}: 8px;"></i>
                        ${t('developer_info_title')}
                    </h3>
                    <div style="
                        background: rgba(96, 165, 250, 0.1);
                        border: 1px solid #60a5fa;
                        border-radius: 8px;
                        padding: 15px;
                        margin-bottom: 15px;
                    ">
                        <p style="color: #ddd; line-height: 1.6; font-size: 0.95rem; margin: 0 0 8px 0;">
                            <strong>${t('developer_name')}</strong>
                        </p>
                        <p style="color: #ddd; line-height: 1.6; font-size: 0.95rem; margin: 0 0 8px 0;">
                            ${t('developer_role')}
                        </p>
                        <p style="color: #ddd; line-height: 1.6; font-size: 0.95rem; margin: 0 0 8px 0;">
                            ${t('developer_experience')}
                        </p>
                        <p style="color: #ddd; line-height: 1.6; font-size: 0.95rem; margin: 0 0 8px 0;">
                            ${t('app_creation_date')}
                        </p>
                        <p style="color: #ddd; line-height: 1.6; font-size: 0.95rem; margin: 0;">
                            ${t('app_platform')}
                        </p>
                    </div>
                </div>

                <!-- Close Button -->
                <div style="text-align: center; margin-top: 25px;">
                    <button onclick="document.getElementById('app-ownership-modal').remove()" style="
                        background: linear-gradient(45deg, #ffcc00, #ffa500);
                        color: #000;
                        border: none;
                        padding: 12px 30px;
                        border-radius: 25px;
                        font-size: 1rem;
                        font-weight: bold;
                        cursor: pointer;
                        transition: transform 0.2s, box-shadow 0.2s;
                        box-shadow: 0 4px 15px rgba(255, 204, 0, 0.3);
                    " onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 20px rgba(255, 204, 0, 0.4)'" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 15px rgba(255, 204, 0, 0.3)'">
                        <i class="fas fa-times" style="margin-${isArabic ? 'left' : 'right'}: 8px;"></i>
                        ${t('close')}
                    </button>
                </div>
            `;

            // Add elements to containers
            modalContainer.appendChild(closeButton);
            modalContainer.appendChild(content);
            modalOverlay.appendChild(modalContainer);

            // Add to body
            document.body.appendChild(modalOverlay);

            // Close modal when clicking outside
            modalOverlay.onclick = (e) => {
                if (e.target === modalOverlay) {
                    modalOverlay.remove();
                }
            };

            // Prevent scrolling on body
            document.body.style.overflow = 'hidden';
            
            // Restore scrolling when modal is closed
            const originalRemove = modalOverlay.remove;
            modalOverlay.remove = function() {
                document.body.style.overflow = '';
                originalRemove.call(this);
            };
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            
            // Set default language
            const savedLanguage = localStorage.getItem('selectedLanguage') || 'ar';
            setLanguage(savedLanguage);
        });
    </script>
</body>
</html>
