<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\modetaris\app\src\main\assets"><file name="additional-fixes.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\additional-fixes.js"/><file name="all.min.css" path="C:\Users\<USER>\modetaris\app\src\main\assets\all.min.css"/><file name="auto-fix-runner.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\auto-fix-runner.js"/><file name="backup-ads-integration.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\backup-ads-integration.js"/><file name="backup-system-blocker.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\backup-system-blocker.js"/><file name="bilingual-dialogs.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\bilingual-dialogs.js"/><file name="column-fix.sql" path="C:\Users\<USER>\modetaris\app\src\main\assets\column-fix.sql"/><file name="COLUMN_ERROR_FIX_REPORT.md" path="C:\Users\<USER>\modetaris\app\src\main\assets\COLUMN_ERROR_FIX_REPORT.md"/><file name="COMPREHENSIVE_SOLUTIONS_REPORT.md" path="C:\Users\<USER>\modetaris\app\src\main\assets\COMPREHENSIVE_SOLUTIONS_REPORT.md"/><file name="create-placeholder.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\create-placeholder.html"/><file name="critical-fixes.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\critical-fixes.js"/><file name="CRITICAL_FIXES_REPORT.md" path="C:\Users\<USER>\modetaris\app\src\main\assets\CRITICAL_FIXES_REPORT.md"/><file name="custom-sections-manager.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\custom-sections-manager.js"/><file name="data-saving-test.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\data-saving-test.html"/><file name="database-error-fixes.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\database-error-fixes.js"/><file name="database-fixes.sql" path="C:\Users\<USER>\modetaris\app\src\main\assets\database-fixes.sql"/><file name="desktop.ini" path="C:\Users\<USER>\modetaris\app\src\main\assets\desktop.ini"/><file name="download-fallback-system.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\download-fallback-system.js"/><file name="emergency-fix.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\emergency-fix.js"/><file name="enhanced-badges.css" path="C:\Users\<USER>\modetaris\app\src\main\assets\enhanced-badges.css"/><file name="file-opening-helper.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\file-opening-helper.js"/><file name="final-fix-executor.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\final-fix-executor.js"/><file name="final-syntax-test.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\final-syntax-test.html"/><file name="firebase-fix-page.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\firebase-fix-page.html"/><file name="fix-images.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\fix-images.html"/><file name="fix-runner.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\fix-runner.html"/><file name="image/1.jpg" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\1.jpg"/><file name="image/2.jpg" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\2.jpg"/><file name="image/3.jpg" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\3.jpg"/><file name="image/4.jpg" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\4.jpg"/><file name="image/5.jpg" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\5.jpg"/><file name="image/6.jpg" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\6.jpg"/><file name="image/app_name.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\app_name.png"/><file name="image/assistant.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\assistant.png"/><file name="image/back.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\back.png"/><file name="image/button-download.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\button-download.png"/><file name="image/close2.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\close2.png"/><file name="image/close_icon.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\close_icon.png"/><file name="image/downarrow.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\downarrow.png"/><file name="image/Download icon.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\Download icon.png"/><file name="image/download_icon2.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\download_icon2.png"/><file name="image/ErrorGlyph_small.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\ErrorGlyph_small.png"/><file name="image/ErrorGlyph_small_hover.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\ErrorGlyph_small_hover.png"/><file name="image/free_icon.svg" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\free_icon.svg"/><file name="image/heart.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\heart.png"/><file name="image/heart.webp" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\heart.webp"/><file name="image/heart_icon.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\heart_icon.png"/><file name="image/heart_icon2.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\heart_icon2.png"/><file name="image/how_install.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\how_install.png"/><file name="image/icon_Addons.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\icon_Addons.png"/><file name="image/icon_apk.jpg" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\icon_apk.jpg"/><file name="image/icon_shaders.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\icon_shaders.png"/><file name="image/important.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\important.png"/><file name="image/koo.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\koo.png"/><file name="image/maps-icon.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\maps-icon.png"/><file name="image/mcpedl.jpg" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\mcpedl.jpg"/><file name="image/menu.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\menu.png"/><file name="image/minecraft-icon.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\minecraft-icon.png"/><file name="image/mineraft_patch.jpg" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\mineraft_patch.jpg"/><file name="image/placeholder.svg" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\placeholder.svg"/><file name="image/search.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\search.png"/><file name="image/seeds-icon.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\seeds-icon.png"/><file name="image/see_all_icon.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\see_all_icon.png"/><file name="image/social/mcpedl.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\social\mcpedl.png"/><file name="image/src_img_icon_2021_icon12.webp" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\src_img_icon_2021_icon12.webp"/><file name="image/src_img_vip_2021_bg25.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\src_img_vip_2021_bg25.png"/><file name="image/suggested_icon.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\suggested_icon.png"/><file name="image/tape.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\tape.png"/><file name="image/tape1.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\tape1.png"/><file name="image/texter.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\texter.png"/><file name="image/Time-icon.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\Time-icon.png"/><file name="image/٢٠٢٥٠١٢١_١٨٥٣٤٣.png" path="C:\Users\<USER>\modetaris\app\src\main\assets\image\٢٠٢٥٠١٢١_١٨٥٣٤٣.png"/><file name="image-display-styles.css" path="C:\Users\<USER>\modetaris\app\src\main\assets\image-display-styles.css"/><file name="image-fix.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\image-fix.js"/><file name="index.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\index.html"/><file name="language-selection.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\language-selection.html"/><file name="libs/font-awesome.min.css" path="C:\Users\<USER>\modetaris\app\src\main\assets\libs\font-awesome.min.css"/><file name="libs/supabase.min.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\libs\supabase.min.js"/><file name="network-handler.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\network-handler.js"/><file name="no-database-fix.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\no-database-fix.js"/><file name="quick-fixes.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\quick-fixes.js"/><file name="remove-backup-system.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\remove-backup-system.html"/><file name="script.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\script.js"/><file name="search.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\search.html"/><file name="search.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\search.js"/><file name="simple-performance-optimizer.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\simple-performance-optimizer.js"/><file name="simple-performance-styles.css" path="C:\Users\<USER>\modetaris\app\src\main\assets\simple-performance-styles.css"/><file name="smart-verification-system.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\smart-verification-system.js"/><file name="social-icons-manager.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\social-icons-manager.js"/><file name="style.css" path="C:\Users\<USER>\modetaris\app\src\main\assets\style.css"/><file name="subscription-page.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\subscription-page.html"/><file name="subscription-page.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\subscription-page.js"/><file name="supabase-manager.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\supabase-manager.js"/><file name="supabase.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\supabase.js"/><file name="system-control-panel.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\system-control-panel.js"/><file name="test-image-fix.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\test-image-fix.html"/><file name="test-image-optimizer.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\test-image-optimizer.html"/><file name="test-performance-fix.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\test-performance-fix.js"/><file name="test-settings-final.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\test-settings-final.html"/><file name="test-settings-integration.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\test-settings-integration.html"/><file name="test-supabase-fix.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\test-supabase-fix.html"/><file name="test-syntax-fix.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\test-syntax-fix.html"/><file name="test-user-settings.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\test-user-settings.html"/><file name="translations.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\translations.js"/><file name="undo-fixes.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\undo-fixes.html"/><file name="unity-ads-test.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\unity-ads-test.js"/><file name="user-settings.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\user-settings.html"/><file name="user-settings.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\user-settings.js"/><file name="ad-test-panel.html" path="C:\Users\<USER>\modetaris\app\src\main\assets\ad-test-panel.html"/><file name="admob-config.js" path="C:\Users\<USER>\modetaris\app\src\main\assets\admob-config.js"/></source></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\modetaris\app\src\release\assets"/></dataSet></merger>