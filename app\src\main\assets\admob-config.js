/**
 * AdMob Configuration and Integration
 * This file handles AdMob integration for the Modetaris app
 */

// AdMob Configuration
const AdMobConfig = {
    // Ad Unit IDs (Production)
    BANNER_AD_UNIT_ID: "ca-app-pub-4373910379376809/6035179861",
    REWARDED_INTERSTITIAL_AD_UNIT_ID: "ca-app-pub-4373910379376809/8741234256",
    
    // Test Mode Configuration
    TEST_MODE: false, // Set to true for testing
    
    // Ad Settings
    BANNER_SETTINGS: {
        name: "banner",
        format: "Banner Ad",
        types: "Text, Images, Interactive Rich Media | Video",
        refresh: "Google Optimized",
        cpm_floor: "Google Optimized (High Floor)"
    },
    
    REWARDED_INTERSTITIAL_SETTINGS: {
        name: "gift mod",
        format: "Rewarded Interstitial Ad",
        reward: "1 mods"
    }
};

// AdMob Integration Functions
const AdMobIntegration = {
    
    /**
     * Initialize AdMob integration
     */
    init() {
        console.log('🚀 Initializing AdMob integration...');
        console.log('AdMob Config:', AdMobConfig);
        
        // Add banner ad placeholder if not exists
        this.createBannerPlaceholder();
        
        // Test AdMob availability
        this.testAdMobAvailability();
    },
    
    /**
     * Create banner ad placeholder in the UI
     */
    createBannerPlaceholder() {
        // Check if banner container already exists
        let bannerContainer = document.getElementById('banner-ad-container');
        
        if (!bannerContainer) {
            bannerContainer = document.createElement('div');
            bannerContainer.id = 'banner-ad-container';
            bannerContainer.className = 'banner-ad-container';
            bannerContainer.style.cssText = `
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                height: 50px;
                background-color: #000;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                border-top: 1px solid #333;
            `;
            
            // Add placeholder text
            bannerContainer.innerHTML = `
                <div style="color: #666; font-size: 12px; text-align: center;">
                    <div>Banner Ad Space</div>
                    <div style="font-size: 10px;">AdMob: ${AdMobConfig.BANNER_AD_UNIT_ID}</div>
                </div>
            `;
            
            // Add to body
            document.body.appendChild(bannerContainer);
            
            // Adjust main content to account for banner
            const mainContent = document.querySelector('.main-content') || document.body;
            if (mainContent) {
                mainContent.style.paddingBottom = '60px';
            }
            
            console.log('✅ Banner ad placeholder created');
        }
    },
    
    /**
     * Test AdMob availability
     */
    testAdMobAvailability() {
        if (typeof Android !== 'undefined') {
            if (Android.testAdMob) {
                console.log('✅ AdMob test function available');
            } else {
                console.warn('⚠️ AdMob test function not available');
            }
            
            if (Android.testShowRewardedAd) {
                console.log('✅ AdMob rewarded ad test function available');
            } else {
                console.warn('⚠️ AdMob rewarded ad test function not available');
            }
        } else {
            console.warn('⚠️ Android interface not available (running in browser?)');
        }
    },
    
    /**
     * Show rewarded interstitial ad for mod download
     */
    showRewardedAdForDownload(modId, modName, downloadUrl) {
        console.log(`🎬 Requesting rewarded ad for mod: ${modName}`);
        
        if (typeof Android !== 'undefined' && Android.requestModDownloadWithAd) {
            // Use the existing download with ad system
            Android.requestModDownloadWithAd(modId, modName, downloadUrl);
        } else {
            console.warn('⚠️ Android download interface not available');
            // Fallback: direct download
            if (typeof Android !== 'undefined' && Android.startModDownload) {
                Android.startModDownload(modId, modName, downloadUrl);
            }
        }
    },
    
    /**
     * Test AdMob functionality
     */
    testAdMob() {
        console.log('🧪 Testing AdMob functionality...');
        
        if (typeof Android !== 'undefined' && Android.testAdMob) {
            Android.testAdMob();
        } else {
            console.warn('⚠️ AdMob test not available');
        }
    },
    
    /**
     * Test rewarded ad
     */
    testRewardedAd() {
        console.log('🎬 Testing rewarded ad...');

        if (typeof Android !== 'undefined' && Android.testShowRewardedAd) {
            Android.testShowRewardedAd();
        } else {
            console.warn('⚠️ Rewarded ad test not available');
        }
    },

    /**
     * Test complete download flow with ad
     */
    testDownloadWithAd() {
        console.log('🧪 Testing complete download flow with ad...');

        if (typeof Android !== 'undefined' && Android.testDownloadWithAd) {
            Android.testDownloadWithAd();
        } else {
            console.warn('⚠️ Download with ad test not available');
        }
    },
    
    /**
     * Get AdMob status
     */
    getStatus() {
        const status = {
            config: AdMobConfig,
            androidAvailable: typeof Android !== 'undefined',
            testFunctionsAvailable: {
                testAdMob: typeof Android !== 'undefined' && !!Android.testAdMob,
                testRewardedAd: typeof Android !== 'undefined' && !!Android.testShowRewardedAd,
                downloadWithAd: typeof Android !== 'undefined' && !!Android.requestModDownloadWithAd
            }
        };
        
        console.log('📊 AdMob Status:', status);
        return status;
    }
};

// Auto-initialize when script loads
document.addEventListener('DOMContentLoaded', () => {
    AdMobIntegration.init();
});

// Make functions globally available
window.AdMobConfig = AdMobConfig;
window.AdMobIntegration = AdMobIntegration;

// Convenience functions for console testing
window.testAdMob = () => AdMobIntegration.testAdMob();
window.testRewardedAd = () => AdMobIntegration.testRewardedAd();
window.testDownloadWithAd = () => AdMobIntegration.testDownloadWithAd();
window.getAdMobStatus = () => AdMobIntegration.getStatus();

console.log('📱 AdMob configuration loaded successfully');
